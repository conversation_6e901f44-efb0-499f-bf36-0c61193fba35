import { styled } from '@topwrite/common';
import { useCallback, useRef, useState } from 'react';
import { BsFileText } from 'react-icons/bs';
import { IoMdSend } from 'react-icons/io';
import ChangedFiles from './changed-files';
import { useContext } from './context';
import TextareaAutosize from 'react-textarea-autosize';

export default function InputBox() {
    const { send, loading, filename, range } = useContext();
    const [focused, setFocused] = useState(false);
    const [query, setQuery] = useState('');
    const textarea = useRef<HTMLTextAreaElement>(null);

    const sendMessage = useCallback(async () => {
        if (query) {
            setQuery('');
            await send(query);
            requestAnimationFrame(() => {
                textarea.current?.focus();
            });
        }
    }, [query]);

    return <Container>
        <ChangedFiles />
        <InputArea $focused={focused}>
            <div className={'d-grid'}>
                <TextareaAutosize
                    ref={textarea}
                    placeholder={'请输入你的任务'}
                    minRows={3}
                    maxRows={5}
                    disabled={loading}
                    value={query}
                    onChange={e => setQuery(e.target.value)}
                    onFocus={() => setFocused(true)}
                    onBlur={() => setFocused(false)}
                    onKeyDown={e => {
                        if (e.key === 'Enter' && !e.shiftKey) {
                            e.preventDefault();
                            sendMessage();
                        }
                    }}
                />
            </div>
            <Toolbar>
                {filename && <Item>
                    <BsFileText className={'me-1'} />
                    {filename}
                    {range && <>#{range.start.line}:{range.start.column}-{range.end.line}:{range.end.column}</>}
                </Item>}
                <InputButton
                    disabled={loading}
                    onClick={sendMessage}
                    className='text-primary'
                >
                    <IoMdSend />
                </InputButton>
            </Toolbar>
        </InputArea>
    </Container>;
}

const Container = styled.div`
    background: var(--ttw-background);
    padding: 1rem;
`;

const InputArea = styled.div<{ $focused?: boolean }>`
    display: flex;
    padding: .5rem;
    flex-direction: column;
    border: var(--bs-border-width) var(--bs-border-style) ${props => props.$focused ? 'var(--bs-primary)' : 'var(--bs-border-color)'};
    border-radius: var(--bs-border-radius-lg);

    textarea {
        border: none;
        outline: none;
        resize: none;
        background: transparent;

        &::placeholder {
            color: rgba(54, 54, 54, .3);
        }
    }
`;

const Toolbar = styled.div`
    display: flex;
    align-items: center;
    gap: .25rem;
    margin-top: .25rem;
`;

const Item = styled.div`
    font-size: 12px;
    color: var(--bs-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid var(--bs-gray-300);
    padding: 0 .5rem;
    height: 22px;
    border-radius: 0.25rem;
`;

const InputButton = styled.button`
    color: var(--bs-secondary);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 1.8rem;
    height: 1.8rem;
    border-radius: .5rem;
    outline: none;
    border: none;
    background: transparent;
    font-size: 1.2rem;
    position: relative;
    margin-left: auto;

    &:disabled {
        color: var(--bs-gray-400) !important;
    }

    &:hover&:not(:disabled) {
        background-color: var(--bs-gray-200);
    }

`;
