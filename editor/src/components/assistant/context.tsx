import { isRequestError, request, RequestConfig, useModel, useOptions } from '@topwrite/common';
import {
    createContext,
    Dispatch,
    PropsWithChildren,
    SetStateAction,
    useCallback,
    useContext as useRcContext,
    useEffect,
    useRef,
    useState
} from 'react';
import useImmer, { Updater } from '../../lib/use-immer';
import { Message } from './message-item';

export interface Conversation {
    id: string;
    title?: string;
    create_time: string;
    messages: Message[];
}

// 窗口状态枚举
export type WindowState = 'chat' | 'history' | 'settings';

interface ContextType {
    filename: string | null;
    range: FileRange | null;
    conversationId?: string;
    setConversationId: Dispatch<SetStateAction<string | undefined>>;
    messages: Message[];
    setMessages: Updater<Message[]>;
    loading: boolean;
    setLoading: Dispatch<SetStateAction<boolean>>;
    send: (query: string) => Promise<void>;
    reset: (conversation?: Conversation) => void;
    // 窗口状态管理
    windowState: WindowState;
    setWindowState: (state: WindowState) => void;
}

const Context = createContext<ContextType | null>(null);

export function Provider({ children, conversation }: PropsWithChildren<{ conversation?: Conversation }>) {
    const { assistant } = useOptions();
    const [{ current: filename, range }] = useModel('workspace');

    // 窗口状态管理
    const [windowState, setWindowState] = useState<WindowState>('chat');

    const getInitialMessages = useCallback((conversation?: Conversation) => {
        const messages: Message[] = [];

        if (conversation) {
            return messages.concat(conversation.messages);
        }
        return messages;
    }, []);

    const [conversationId, setConversationId] = useState<string | undefined>(conversation?.id);
    const conversationRef = useRef(conversationId);
    useEffect(() => {
        conversationRef.current = conversationId;
    }, [conversationId]);

    const [messages, setMessages] = useImmer<Message[]>(() => {
        return getInitialMessages(conversation);
    });

    const controller = useRef<AbortController | null>(null);
    const [loading, setLoading] = useState(false);

    const send = useCallback(async (query: string) => {
        if (query) {
            if (controller.current) {
                controller.current.abort();
            }
            setLoading(true);

            const input = {
                query,
                context: filename ? {
                    filename,
                    range
                } : undefined
            };

            setMessages((messages) => {
                messages.push(
                    {
                        input: input,
                        output: [],
                        loading: true,
                    },
                );
            });

            try {
                let config: RequestConfig = {
                    method: 'post',
                    url: `${assistant}/chat`,
                    data: {
                        input,
                        conversation: conversationRef.current,
                    },
                    onMessage: (message) => {
                        if (message.data) {
                            if (message.data != '[DONE]') {
                                try {
                                    const event = JSON.parse(message.data);
                                    if (event.conversation) {
                                        conversationRef.current = event.conversation;
                                        setConversationId(event.conversation);
                                    } else {
                                        setMessages((messages) => {
                                            const message = messages[messages.length - 1];
                                            if (message.output) {
                                                if (event.chunks) {
                                                    //更新消息
                                                    const chunkIndex: number = event.chunks.index;
                                                    if (!message.output[chunkIndex]) {
                                                        message.output[chunkIndex] = {
                                                            content: '',
                                                        };
                                                    }
                                                    if (event.chunks.error) {
                                                        message.output[chunkIndex].error = event.chunks.error;
                                                    } else if (event.chunks.tools) {
                                                        if (!message.output[chunkIndex].tools) {
                                                            message.output[chunkIndex].tools = [];
                                                        }
                                                        const toolIndex = event.chunks.tools.index;
                                                        if ('response' in event.chunks.tools) {
                                                            message.output[chunkIndex].tools[toolIndex].response = event.chunks.tools.response;
                                                            message.output[chunkIndex].tools[toolIndex].error = event.chunks.tools.error;
                                                            message.output[chunkIndex].tools[toolIndex].content = event.chunks.tools.content;
                                                        } else {
                                                            message.output[chunkIndex].tools[toolIndex] = {
                                                                name: event.chunks.tools.name,
                                                                title: event.chunks.tools.title,
                                                                arguments: event.chunks.tools.arguments
                                                            };
                                                        }
                                                    } else if (event.chunks.content) {
                                                        if (typeof event.chunks.content === 'object') {
                                                            if (!Array.isArray(message.output[chunkIndex].content)) {
                                                                message.output[chunkIndex].content = [];
                                                            }
                                                            const contentIndex: number = event.chunks.content.index;
                                                            const contentValue = event.chunks.content.value;

                                                            if (typeof contentValue === 'string') {
                                                                if (!message.output[chunkIndex].content[contentIndex]) {
                                                                    message.output[chunkIndex].content[contentIndex] = '';
                                                                }
                                                                message.output[chunkIndex].content[contentIndex] += contentValue;
                                                            } else {
                                                                message.output[chunkIndex].content[contentIndex] = contentValue;
                                                            }
                                                        } else {
                                                            message.output[chunkIndex].content += event.chunks.content;
                                                        }
                                                    }
                                                } else if (event.id) {
                                                    message.id = event.id;
                                                }
                                            }
                                        });
                                    }
                                } catch (e) {
                                    console.error(e);
                                }
                            } else {
                                setMessages((messages) => {
                                    const message = messages[messages.length - 1];
                                    message.loading = false;
                                });
                            }
                        }
                    },
                };

                await request(config);

            } catch (e) {
                let errors = '未知错误';
                if (isRequestError(e)) {
                    if (e.response?.status == 401) {
                        errors = '未授权或授权已过期，请刷新页面后重试';
                    } else {
                        if (typeof e.errors === 'string') {
                            errors = e.errors;
                        } else {
                            errors = Object.values(e.errors).join('\n');
                        }
                    }
                }
                setMessages((messages) => {
                    const message = messages[messages.length - 1];
                    if (message.output) {
                        if (message.output.length === 0) {
                            message.output = [{
                                content: `[${errors}]`,
                                tools: []
                            }];
                        } else {
                            message.output[message.output.length - 1].content = `[${errors}]`;
                        }
                    }
                    message.loading = false;
                });
            }

            setLoading(false);
        }
    }, [filename, range]);

    const reset = useCallback((conversation?: Conversation) => {
        if (!loading) {
            setConversationId(conversation?.id);
            setMessages(getInitialMessages(conversation));
        }
    }, [loading, getInitialMessages]);

    return <Context.Provider value={{
        filename,
        range,
        conversationId,
        setConversationId,
        messages,
        setMessages,
        loading,
        setLoading,
        send,
        reset,
        windowState,
        setWindowState
    }}>
        {children}
    </Context.Provider>;
}

export function useContext() {
    const context = useRcContext(Context);
    if (!context) {
        throw new Error('useContext must be used within a Provider');
    }
    return context;
}
